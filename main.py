#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多开软件主程序
类似雷电模拟器的多开管理工具
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.simple_main_window import SimpleMainWindow
from src.core.config_manager import ConfigManager
from src.core.instance_manager import InstanceManager
from src.utils.admin_utils import check_admin_and_prompt, is_admin

class MultiOpenApp:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.instance_manager = InstanceManager(self.config_manager)
        self.main_window = None
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 首先应用网络修复，解决QingTalk网络接口错误
            self.apply_network_fix()

            # 检查管理员权限
            admin_status = is_admin()
            if admin_status:
                print("✅ 程序以管理员权限运行")
                print("🌟 终极模式已启用：设备信息伪装 + 完美多开 + 持久化登录")
            else:
                print("⚠️ 程序以普通权限运行，部分功能可能受限")
                print("💡 完美模式：完美多开 + 持久化登录（无设备信息伪装）")
                print("💡 提示：以管理员身份运行可启用终极模式")

            # 加载配置
            self.config_manager.load_config()

            # 创建简化主窗口
            self.main_window = SimpleMainWindow(self.instance_manager)

            # 简化界面自己处理关闭事件

            return True
        except Exception as e:
            messagebox.showerror("初始化错误", f"程序初始化失败: {str(e)}")
            return False

    def apply_network_fix(self):
        """应用网络修复，解决QingTalk网络接口错误"""
        try:
            print("🔧 应用网络修复...")

            import os
            import subprocess
            import tempfile
            import json

            # 1. 刷新DNS缓存
            try:
                subprocess.run(['ipconfig', '/flushdns'],
                              capture_output=True, timeout=5)
                print("  ✅ DNS缓存已刷新")
            except Exception:
                pass

            # 2. 启用安全网络模式
            try:
                from src.utils.safe_network_fix import disable_network_modifications, apply_safe_network_fix

                # 禁用所有网络修改功能
                disable_network_modifications()
                print("  🛡️ 已启用安全网络模式，禁用真实网络接口修改")

                # 应用安全网络修复
                env_dict = os.environ.copy()
                env_dict = apply_safe_network_fix(env_dict, "main_process")
                os.environ.update(env_dict)

            except Exception as e:
                print(f"  ⚠️ 安全网络修复失败: {e}")
                # 应用最基础的安全修复
                os.environ.update({
                    'DISABLE_MAC_MODIFICATION': '1',
                    'SAFE_NETWORK_MODE': '1',
                    'DISABLE_NETWORK_INTERFACE_ENUM': '1',
                    'UV_THREADPOOL_SIZE': '16'
                })

            # 3. 设置基础网络环境变量
            network_env = {
                'USERDNSDOMAIN': 'local',
                'CLIENTNAME': 'QingTalkPC',
                'SESSIONNAME': 'Console',
                'HOMEDRIVE': os.environ.get('HOMEDRIVE', 'C:'),
                'HOMEPATH': os.environ.get('HOMEPATH', '\\Users\\Administrator'),
                'LOCALAPPDATA': os.environ.get('LOCALAPPDATA', 'C:\\Users\\<USER>\\AppData\\Local'),
                'APPDATA': os.environ.get('APPDATA', 'C:\\Users\\<USER>\\AppData\\Roaming'),
                'WINDIR': os.environ.get('WINDIR', 'C:\\Windows'),
                'SYSTEMROOT': os.environ.get('SYSTEMROOT', 'C:\\Windows'),
                'PATHEXT': os.environ.get('PATHEXT', '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'),
                # 增强的网络相关环境变量
                'NODE_OPTIONS': '--max-old-space-size=4096 --no-deprecation',
                'UV_THREADPOOL_SIZE': '8',  # 增加线程池大小
                'ELECTRON_DISABLE_SECURITY_WARNINGS': 'true',
                'DISABLE_NETWORK_INTERFACE_ENUM': '1',  # 禁用网络接口枚举
            }

            for key, value in network_env.items():
                os.environ[key] = value

            # 4. 创建增强的临时网络配置
            try:
                from src.utils.network_fix import create_enhanced_network_fix
                temp_dir = create_enhanced_network_fix()
                if temp_dir:
                    os.environ['QINGTALK_NETWORK_FIX'] = temp_dir
                    os.environ['ENHANCED_NETWORK_FIX'] = temp_dir
                    print("  ✅ 增强网络修复已应用")
                else:
                    # 回退到基础配置
                    temp_dir = tempfile.mkdtemp(prefix="qingtalk_network_")

                    # 创建基础网络接口配置
                    network_config = {
                        "interfaces": {
                            "Ethernet": {
                                "address": "*************",
                                "netmask": "*************",
                                "family": "IPv4",
                                "mac": "00:15:5D:FF:FF:FF",
                                "internal": False
                            },
                            "Loopback": {
                                "address": "127.0.0.1",
                                "netmask": "*********",
                                "family": "IPv4",
                                "mac": "00:00:00:00:00:00",
                                "internal": True
                            }
                        }
                    }

                    config_file = os.path.join(temp_dir, "network_interfaces.json")
                    with open(config_file, 'w') as f:
                        json.dump(network_config, f, indent=2)

                    # 创建hosts文件
                    hosts_content = """# QingTalk Network Fix
127.0.0.1 localhost
::1 localhost
127.0.0.1 local
"""
                    hosts_file = os.path.join(temp_dir, "hosts")
                    with open(hosts_file, 'w') as f:
                        f.write(hosts_content)

                    # 设置环境变量
                    os.environ['QINGTALK_NETWORK_FIX'] = temp_dir
                    os.environ['TEMP_NETWORK_CONFIG'] = config_file
                    print("  ✅ 基础网络修复已应用")
            except Exception as e:
                print(f"  ⚠️ 网络配置创建失败: {e}")
                print("  💡 将使用最小化网络修复")

        except Exception as e:
            print(f"  ⚠️ 网络修复失败: {e}")
            # 即使失败也继续运行
    
    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 停止所有运行中的实例
            self.instance_manager.stop_all_instances()

            # 保存配置
            self.config_manager.save_config()

        except Exception as e:
            print(f"关闭程序时出错: {e}")
    
    def run(self):
        """运行应用程序"""
        if self.initialize():
            # 简化界面有自己的root，不需要使用这个root
            self.main_window.run()

def main():
    """主函数"""
    try:
        app = MultiOpenApp()
        app.run()
    except Exception as e:
        messagebox.showerror("程序错误", f"程序运行出错: {str(e)}")

if __name__ == "__main__":
    main()
