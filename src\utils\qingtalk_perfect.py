#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QingTalk完美方案
结合持久化登录 + 程序副本隔离 = 多开 + 保持登录状态
"""

import os
import subprocess
import shutil
import time
from typing import Optional

def launch_qingtalk_perfect(exe_path: str, instance_id: str) -> Optional[subprocess.Popen]:
    """
    QingTalk完美启动方案
    多开 + 持久化登录
    """
    try:
        print(f"🌟 QingTalk完美启动 (实例: {instance_id[:8]})")
        
        # 1. 创建持久化环境
        persistent_base = os.path.join(os.getcwd(), "persistent_data")
        os.makedirs(persistent_base, exist_ok=True)
        
        instance_dir = os.path.join(persistent_base, f"instance_{instance_id[:8]}")
        qingtalk_program_dir = os.path.join(instance_dir, "QingTalk")
        
        # 检查是否是首次启动
        is_first_time = not os.path.exists(instance_dir)
        
        if is_first_time:
            print(f"  🆕 首次启动，创建完整环境...")
            os.makedirs(instance_dir, exist_ok=True)
            
            # 复制QingTalk程序
            qingtalk_source_dir = os.path.dirname(exe_path)
            print(f"  📂 复制QingTalk程序...")
            shutil.copytree(qingtalk_source_dir, qingtalk_program_dir, 
                           ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
            
            # 创建伪装程序
            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
            disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id[:8]}.exe")
            
            if os.path.exists(original_exe):
                shutil.copy2(original_exe, disguised_exe)
                print(f"  🎭 创建伪装程序: QingTalk_{instance_id[:8]}.exe")
        else:
            print(f"  🔄 重用现有环境...")

            # 检查程序目录是否完整
            if not os.path.exists(qingtalk_program_dir):
                print(f"  ❌ 程序目录不存在: {qingtalk_program_dir}")
                print(f"  🔄 重新创建程序目录...")

                # 重新复制程序
                qingtalk_source_dir = os.path.dirname(exe_path)
                shutil.copytree(qingtalk_source_dir, qingtalk_program_dir,
                               ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
                print(f"  ✅ 程序目录重新创建完成")

            # 检查原始程序是否存在
            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
            if not os.path.exists(original_exe):
                print(f"  ❌ 原始程序不存在: {original_exe}")
                print(f"  📂 程序目录内容: {os.listdir(qingtalk_program_dir) if os.path.exists(qingtalk_program_dir) else '目录不存在'}")

                # 重新复制程序
                qingtalk_source_dir = os.path.dirname(exe_path)
                if os.path.exists(qingtalk_program_dir):
                    shutil.rmtree(qingtalk_program_dir)
                shutil.copytree(qingtalk_source_dir, qingtalk_program_dir,
                               ignore=shutil.ignore_patterns('*.log', '*.tmp', 'userData'))
                print(f"  ✅ 程序重新复制完成")
        
        # 使用伪装的可执行文件
        disguised_exe = os.path.join(qingtalk_program_dir, f"QingTalk_{instance_id[:8]}.exe")

        if not os.path.exists(disguised_exe):
            # 如果伪装文件不存在，创建它
            original_exe = os.path.join(qingtalk_program_dir, "QingTalk.exe")
            if os.path.exists(original_exe):
                shutil.copy2(original_exe, disguised_exe)
                print(f"  🎭 创建伪装程序: QingTalk_{instance_id[:8]}.exe")
            else:
                print(f"  ❌ 原始程序不存在: {original_exe}")
                raise FileNotFoundError(f"原始QingTalk.exe不存在: {original_exe}")

        # 验证伪装程序是否存在
        if not os.path.exists(disguised_exe):
            print(f"  ❌ 伪装程序不存在: {disguised_exe}")
            raise FileNotFoundError(f"伪装程序不存在: {disguised_exe}")

        print(f"  ✅ 伪装程序确认存在: {disguised_exe}")
        
        # 2. 创建完整的用户环境（持久化登录）
        user_data_dir = os.path.join(instance_dir, 'UserData')
        
        # Windows用户目录结构
        appdata_roaming = os.path.join(user_data_dir, 'AppData', 'Roaming')
        appdata_local = os.path.join(user_data_dir, 'AppData', 'Local')
        appdata_locallow = os.path.join(user_data_dir, 'AppData', 'LocalLow')
        temp_dir = os.path.join(user_data_dir, 'Temp')
        documents_dir = os.path.join(user_data_dir, 'Documents')
        
        # 创建目录结构
        for path in [appdata_roaming, appdata_local, appdata_locallow, temp_dir, documents_dir]:
            os.makedirs(path, exist_ok=True)
        
        # 创建QingTalk专用目录
        qingtalk_roaming = os.path.join(appdata_roaming, 'QingTalk')
        qingtalk_local = os.path.join(appdata_local, 'QingTalk')
        qingtalk_locallow = os.path.join(appdata_locallow, 'QingTalk')
        qingtalk_documents = os.path.join(documents_dir, 'QingTalk')
        
        for path in [qingtalk_roaming, qingtalk_local, qingtalk_locallow, qingtalk_documents]:
            os.makedirs(path, exist_ok=True)
        
        # 3. 检查并复制系统QingTalk数据（确保登录状态）
        need_copy_data = is_first_time

        # 如果不是首次启动，检查是否有登录数据
        if not is_first_time:
            config_file = os.path.join(qingtalk_roaming, 'config.json')
            if not os.path.exists(config_file):
                print(f"  ⚠️ 检测到缺少登录配置，重新复制系统数据...")
                need_copy_data = True

        if need_copy_data:
            print(f"  📋 复制系统QingTalk数据...")

            # 复制系统中的QingTalk数据（如果存在）
            system_locations = [
                (os.path.expanduser("~\\AppData\\Roaming\\QingTalk"), qingtalk_roaming),
                (os.path.expanduser("~\\AppData\\Local\\QingTalk"), qingtalk_local),
                (os.path.expanduser("~\\AppData\\LocalLow\\QingTalk"), qingtalk_locallow),
                (os.path.expanduser("~\\Documents\\QingTalk"), qingtalk_documents),
            ]

            for src, dst in system_locations:
                if os.path.exists(src):
                    try:
                        # 复制现有数据
                        if os.path.exists(dst):
                            shutil.rmtree(dst)
                        shutil.copytree(src, dst)
                        print(f"    ✅ 复制: {src} -> {dst}")
                    except Exception as e:
                        print(f"    ⚠️ 复制失败: {src} - {e}")
                        # 确保目录存在
                        os.makedirs(dst, exist_ok=True)
        else:
            print(f"  💾 使用现有登录数据...")
        
        # 4. 设置完全隔离的环境变量
        env = os.environ.copy()
        
        # 基础用户数据重定向
        env.update({
            'APPDATA': appdata_roaming,
            'LOCALAPPDATA': appdata_local,
            'USERPROFILE': user_data_dir,
            'TEMP': temp_dir,
            'TMP': temp_dir,
            'HOMEDRIVE': os.path.splitdrive(user_data_dir)[0],
            'HOMEPATH': os.path.splitdrive(user_data_dir)[1],
        })
        
        # 系统标识完全伪造（避免检测）
        unique_suffix = instance_id[:8]

        # 生成基于实例ID的固定设备标识（确保重启后一致）
        import random
        import string
        import hashlib

        # 使用实例ID作为随机种子，确保每次生成相同的设备信息
        seed_value = int(hashlib.md5(instance_id.encode()).hexdigest()[:8], 16)
        random.seed(seed_value)

        # 生成固定的计算机名（基于实例ID）
        computer_formats = [
            f'DESKTOP-{unique_suffix.upper()}',
            f'PC-{unique_suffix.upper()}',
            f'WIN-{"".join(random.choices(string.ascii_uppercase + string.digits, k=8))}',
            f'LAPTOP-{unique_suffix.upper()}',
            f'WORKSTATION-{unique_suffix.upper()[:6]}'
        ]
        fake_computer = random.choice(computer_formats)

        # 生成固定的用户名
        user_names = [f'User{unique_suffix}', f'Admin{unique_suffix}', f'PC{unique_suffix}', f'Work{unique_suffix}']
        fake_user = random.choice(user_names)

        env.update({
            'COMPUTERNAME': fake_computer,
            'USERNAME': fake_user,
            'USERDOMAIN': fake_computer,
            'SESSIONNAME': f'Console',
            'LOGONSERVER': f'\\\\{fake_computer}',
            'USERDNSDOMAIN': f'{fake_computer}.local',
            'CLIENTNAME': fake_computer,
            # 添加网络修复相关环境变量
            'NODE_OPTIONS': '--max-old-space-size=4096',
            'UV_THREADPOOL_SIZE': '4',
            'WINDIR': os.environ.get('WINDIR', 'C:\\Windows'),
            'SYSTEMROOT': os.environ.get('SYSTEMROOT', 'C:\\Windows'),
        })

        # 应用网络环境修复，避免网络接口错误
        try:
            from .network_fix import apply_network_environment_fix, create_qingtalk_network_fix
            from .network_interface_fix import apply_network_interface_fix_env, diagnose_network_interface_error

            # 先诊断网络接口问题
            issues = diagnose_network_interface_error()
            if issues:
                print(f"  ⚠️ 检测到网络接口问题，应用专项修复...")
                env = apply_network_interface_fix_env(env)

            # 应用基础网络修复
            env = apply_network_environment_fix(env, fake_computer)

            # 创建QingTalk专用网络修复
            network_fix_dir = create_qingtalk_network_fix()
            if network_fix_dir:
                env['QINGTALK_NETWORK_FIX'] = network_fix_dir

        except Exception as e:
            print(f"  ⚠️ 网络环境修复失败: {e}")
            # 应用最基础的网络修复
            env.update({
                'UV_THREADPOOL_SIZE': '8',
                'NODE_OPTIONS': '--max-old-space-size=4096 --no-deprecation',
                'DISABLE_NETWORK_INTERFACE_ENUM': '1',
                'FORCE_LOCALHOST_ONLY': '1'
            })
        
        # 硬件标识伪造（基于实例ID固定生成）
        cpu_models = [
            f'Intel64 Family 6 Model {sum(ord(c) for c in instance_id) % 100} Stepping 1',
            f'Intel64 Family 6 Model {(sum(ord(c) for c in instance_id) + 10) % 100} Stepping 2',
            f'AMD64 Family 23 Model {sum(ord(c) for c in instance_id) % 50} Stepping 1',
            f'Intel64 Family 6 Model {(sum(ord(c) for c in instance_id) + 20) % 100} Stepping 3'
        ]

        processor_counts = ['2', '4', '6', '8', '12', '16']
        processor_levels = ['6', '15', '23']

        env.update({
            'PROCESSOR_IDENTIFIER': random.choice(cpu_models),
            'PROCESSOR_ARCHITECTURE': random.choice(['AMD64', 'x86_64']),
            'NUMBER_OF_PROCESSORS': random.choice(processor_counts),
            'PROCESSOR_LEVEL': random.choice(processor_levels),
            'PROCESSOR_REVISION': f'{sum(ord(c) for c in instance_id) % 9999:04x}',
        })

        # Windows版本信息伪造
        windows_versions = [
            ('10.0', '19041'),  # Windows 10 20H1
            ('10.0', '19042'),  # Windows 10 20H2
            ('10.0', '19043'),  # Windows 10 21H1
            ('10.0', '19044'),  # Windows 10 21H2
            ('10.0', '22000'),  # Windows 11 21H2
            ('10.0', '22621'),  # Windows 11 22H2
        ]

        win_version, win_build = random.choice(windows_versions)
        env.update({
            'OS': 'Windows_NT',
            'WINVER': win_version,
            'WINBUILD': win_build,
        })

        # 系统唯一标识符（基于实例ID固定生成）
        system_uuid = f'{unique_suffix}-{"".join(random.choices(string.ascii_lowercase + string.digits, k=4))}-{"".join(random.choices(string.ascii_lowercase + string.digits, k=4))}-{"".join(random.choices(string.ascii_lowercase + string.digits, k=12))}'
        env.update({
            'SYSTEM_UUID': system_uuid,
            'MACHINE_GUID': system_uuid.replace('-', '').upper(),
        })
        
        # 网络隔离
        port_base = 40000 + (sum(ord(c) for c in instance_id) % 10000)
        env.update({
            'QINGTALK_PORT_BASE': str(port_base),
            'QINGTALK_IPC_PORT': str(port_base + 1),
            'QINGTALK_SYNC_PORT': str(port_base + 2),
            'QINGTALK_UPDATE_PORT': str(port_base + 3),
        })
        
        # 进程通信隔离
        env.update({
            'QINGTALK_INSTANCE_ID': instance_id,
            'QINGTALK_ISOLATION_MODE': '1',
            'QINGTALK_MUTEX_PREFIX': f'QT_{unique_suffix}_',
            'QINGTALK_PIPE_PREFIX': f'QTPipe_{unique_suffix}_',
            'QINGTALK_SHARED_MEM_PREFIX': f'QTMem_{unique_suffix}_',
        })
        
        # 网络适配器信息伪造
        mac_prefixes = ['00:1B:44', '00:50:56', '08:00:27', '0A:00:27', '00:0C:29', '00:15:5D']
        fake_mac = f'{random.choice(mac_prefixes)}:{random.randint(10,99):02X}:{random.randint(10,99):02X}:{random.randint(10,99):02X}'

        env.update({
            'MAC_ADDRESS': fake_mac,
            'NETWORK_ADAPTER': f'Intel(R) Ethernet Connection {random.choice(["I217-LM", "I218-V", "I219-V", "82579LM"])}',
        })

        # BIOS信息伪造
        bios_vendors = ['American Megatrends Inc.', 'Phoenix Technologies', 'Award Software', 'Insyde Corp.']
        bios_versions = [f'{random.choice(["F", "A", "B"])}{random.randint(10,99)}', f'{random.randint(1,9)}.{random.randint(10,99)}']

        env.update({
            'BIOS_VENDOR': random.choice(bios_vendors),
            'BIOS_VERSION': random.choice(bios_versions),
            'BIOS_DATE': f'{random.randint(1,12):02d}/{random.randint(1,28):02d}/{random.randint(2020,2024)}',
        })

        # 主板信息伪造
        motherboard_manufacturers = ['ASUSTeK Computer INC.', 'MSI', 'Gigabyte Technology', 'ASRock']
        motherboard_models = [f'{random.choice(["B", "H", "Z"])}{random.randint(400,600)}{random.choice(["M", ""])}-{random.choice(["PRO", "PLUS", "A"])}']

        env.update({
            'MOTHERBOARD_MANUFACTURER': random.choice(motherboard_manufacturers),
            'MOTHERBOARD_MODEL': random.choice(motherboard_models),
        })

        # Windows目录隔离
        env.update({
            'ALLUSERSPROFILE': os.path.join(user_data_dir, 'ProgramData'),
            'PROGRAMDATA': os.path.join(user_data_dir, 'ProgramData'),
            'PUBLIC': os.path.join(user_data_dir, 'Public'),
        })
        
        # 创建必要目录
        for env_path in [env['ALLUSERSPROFILE'], env['PUBLIC']]:
            os.makedirs(env_path, exist_ok=True)
        
        print(f"  📂 程序目录: {qingtalk_program_dir}")
        print(f"  🎭 伪装程序: QingTalk_{instance_id[:8]}.exe")
        print(f"  💾 数据目录: {user_data_dir}")
        print(f"  🖥️ 伪造计算机: {fake_computer}")
        print(f"  👤 伪造用户: {fake_user}")
        print(f"  🔧 端口基数: {port_base}")
        print(f"  🌐 MAC地址: {fake_mac}")
        print(f"  💻 处理器: {env['PROCESSOR_IDENTIFIER'][:50]}...")
        print(f"  🔧 Windows版本: {win_version} Build {win_build}")
        print(f"  {'🆕 首次创建' if is_first_time else '🔄 重用现有'}")
        
        # 5. 安全启动伪装的QingTalk
        creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP

        try:
            # 使用安全启动器
            from .qingtalk_launcher import launch_qingtalk_safe
            process = launch_qingtalk_safe(disguised_exe, env=env)
            if not process:
                # 如果安全启动失败，回退到普通启动
                print(f"  🔄 回退到普通启动...")
                process = subprocess.Popen(
                    disguised_exe,
                    env=env,
                    cwd=qingtalk_program_dir,
                    creationflags=creation_flags
                )
        except Exception as e:
            print(f"  ⚠️ 安全启动器异常: {e}")
            # 回退到普通启动
            process = subprocess.Popen(
                disguised_exe,
                env=env,
                cwd=qingtalk_program_dir,
                creationflags=creation_flags
            )
        
        # 保存信息
        process.isolation_info = {
            'program_dir': qingtalk_program_dir,
            'user_data_dir': instance_dir,
            'disguised_exe': disguised_exe,
            'method': 'qingtalk_perfect',
            'instance_id': instance_id,
            'port_base': port_base,
            'persistent': True,
            'is_first_time': is_first_time,
            'qingtalk_data_dirs': {
                'roaming': qingtalk_roaming,
                'local': qingtalk_local,
                'locallow': qingtalk_locallow,
                'documents': qingtalk_documents,
            },
            'device_info': {
                'fake_computer': fake_computer,
                'fake_user': fake_user,
                'mac_address': fake_mac,
                'processor': env['PROCESSOR_IDENTIFIER'],
                'windows_version': f"{win_version} Build {win_build}",
                'bios_vendor': env['BIOS_VENDOR'],
                'motherboard': env['MOTHERBOARD_MANUFACTURER'],
            }
        }
        
        print(f"  ✅ QingTalk完美启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  ❌ QingTalk完美启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_qingtalk_perfect(process):
    """清理完美方案资源（保持所有数据）"""
    try:
        if hasattr(process, 'isolation_info'):
            isolation_info = process.isolation_info
            instance_id = isolation_info.get('instance_id', 'unknown')
            
            # 不删除任何数据，完全保持登录状态和程序
            print(f"🌟 完全保持实例 {instance_id[:8]} 的程序和登录状态")
            print(f"📂 程序目录保留: {isolation_info.get('program_dir', 'unknown')}")
            print(f"💾 数据目录保留: {isolation_info.get('user_data_dir', 'unknown')}")
            print(f"🎭 伪装程序保留: {isolation_info.get('disguised_exe', 'unknown')}")
            
            qingtalk_dirs = isolation_info.get('qingtalk_data_dirs', {})
            for location, path in qingtalk_dirs.items():
                print(f"💾 QingTalk {location} 数据保留: {path}")
            
    except Exception as e:
        print(f"⚠️ 清理完美方案资源失败: {e}")

if __name__ == "__main__":
    # 测试完美方案
    qingtalk_exe = r"C:/Program Files/QingTalk/QingTalk/QingTalk.exe"
    
    if os.path.exists(qingtalk_exe):
        print("测试QingTalk完美方案")
        
        # 启动两个实例
        import uuid
        process1 = launch_qingtalk_perfect(qingtalk_exe, str(uuid.uuid4()))
        time.sleep(5)
        process2 = launch_qingtalk_perfect(qingtalk_exe, str(uuid.uuid4()))
        
        if process1 and process2:
            print("🎉 两个实例都启动成功！")
            
            # 等待测试
            input("按回车键停止测试...")
            
            # 清理
            if process1.poll() is None:
                process1.terminate()
                cleanup_qingtalk_perfect(process1)
            
            if process2.poll() is None:
                process2.terminate()
                cleanup_qingtalk_perfect(process2)
        else:
            print("❌ 启动失败")
    else:
        print("❌ QingTalk程序不存在")
