#pragma once

#if (defined(_WIN32) || defined(__CYGWIN__)) && !defined(GIO_STATIC_COMPILATION)
#  define _GIO_EXPORT __declspec(dllexport)
#  define _GIO_IMPORT __declspec(dllimport)
#elif __GNUC__ >= 4
#  define _GIO_EXPORT __attribute__((visibility("default")))
#  define _GIO_IMPORT
#else
#  define _GIO_EXPORT
#  define _GIO_IMPORT
#endif
#ifdef GIO_COMPILATION
#  define _GIO_API _GIO_EXPORT
#else
#  define _GIO_API _GIO_IMPORT
#endif

#define _GIO_EXTERN _GIO_API extern

#define GIO_VAR _GIO_EXTERN
#define GIO_AVAILABLE_IN_ALL _GIO_EXTERN

#ifdef GLIB_DISABLE_DEPRECATION_WARNINGS
#define GIO_DEPRECATED _GIO_EXTERN
#define GIO_DEPRECATED_FOR(f) _GIO_EXTERN
#define GIO_UNAVAILABLE(maj,min) _GIO_EXTERN
#define GIO_UNAVAILABLE_STATIC_INLINE(maj,min)
#else
#define GIO_DEPRECATED G_DEPRECATED _GIO_EXTERN
#define GIO_DEPRECATED_FOR(f) G_DEPRECATED_FOR(f) _GIO_EXTERN
#define GIO_UNAVAILABLE(maj,min) G_UNAVAILABLE(maj,min) _GIO_EXTERN
#define GIO_UNAVAILABLE_STATIC_INLINE(maj,min) G_UNAVAILABLE(maj,min)
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_26
#define GIO_DEPRECATED_IN_2_26 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_26_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_26 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_26_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_26 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_26_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_26 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_26_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_26 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_26_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_26
#define GIO_DEPRECATED_MACRO_IN_2_26_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_26
#define GIO_DEPRECATED_ENUMERATOR_IN_2_26_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_26
#define GIO_DEPRECATED_TYPE_IN_2_26_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_26
#define GIO_AVAILABLE_IN_2_26 GIO_UNAVAILABLE (2, 26)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_26 GLIB_UNAVAILABLE_STATIC_INLINE (2, 26)
#define GIO_AVAILABLE_MACRO_IN_2_26 GLIB_UNAVAILABLE_MACRO (2, 26)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_26 GLIB_UNAVAILABLE_ENUMERATOR (2, 26)
#define GIO_AVAILABLE_TYPE_IN_2_26 GLIB_UNAVAILABLE_TYPE (2, 26)
#else
#define GIO_AVAILABLE_IN_2_26 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_26
#define GIO_AVAILABLE_MACRO_IN_2_26
#define GIO_AVAILABLE_ENUMERATOR_IN_2_26
#define GIO_AVAILABLE_TYPE_IN_2_26
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_28
#define GIO_DEPRECATED_IN_2_28 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_28_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_28 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_28_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_28 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_28_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_28 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_28_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_28 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_28_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_28
#define GIO_DEPRECATED_MACRO_IN_2_28_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_28
#define GIO_DEPRECATED_ENUMERATOR_IN_2_28_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_28
#define GIO_DEPRECATED_TYPE_IN_2_28_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_28
#define GIO_AVAILABLE_IN_2_28 GIO_UNAVAILABLE (2, 28)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_28 GLIB_UNAVAILABLE_STATIC_INLINE (2, 28)
#define GIO_AVAILABLE_MACRO_IN_2_28 GLIB_UNAVAILABLE_MACRO (2, 28)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_28 GLIB_UNAVAILABLE_ENUMERATOR (2, 28)
#define GIO_AVAILABLE_TYPE_IN_2_28 GLIB_UNAVAILABLE_TYPE (2, 28)
#else
#define GIO_AVAILABLE_IN_2_28 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_28
#define GIO_AVAILABLE_MACRO_IN_2_28
#define GIO_AVAILABLE_ENUMERATOR_IN_2_28
#define GIO_AVAILABLE_TYPE_IN_2_28
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_30
#define GIO_DEPRECATED_IN_2_30 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_30_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_30 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_30_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_30 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_30_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_30 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_30_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_30 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_30_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_30
#define GIO_DEPRECATED_MACRO_IN_2_30_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_30
#define GIO_DEPRECATED_ENUMERATOR_IN_2_30_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_30
#define GIO_DEPRECATED_TYPE_IN_2_30_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_30
#define GIO_AVAILABLE_IN_2_30 GIO_UNAVAILABLE (2, 30)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_30 GLIB_UNAVAILABLE_STATIC_INLINE (2, 30)
#define GIO_AVAILABLE_MACRO_IN_2_30 GLIB_UNAVAILABLE_MACRO (2, 30)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_30 GLIB_UNAVAILABLE_ENUMERATOR (2, 30)
#define GIO_AVAILABLE_TYPE_IN_2_30 GLIB_UNAVAILABLE_TYPE (2, 30)
#else
#define GIO_AVAILABLE_IN_2_30 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_30
#define GIO_AVAILABLE_MACRO_IN_2_30
#define GIO_AVAILABLE_ENUMERATOR_IN_2_30
#define GIO_AVAILABLE_TYPE_IN_2_30
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_32
#define GIO_DEPRECATED_IN_2_32 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_32_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_32 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_32_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_32 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_32_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_32 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_32_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_32 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_32_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_32
#define GIO_DEPRECATED_MACRO_IN_2_32_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_32
#define GIO_DEPRECATED_ENUMERATOR_IN_2_32_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_32
#define GIO_DEPRECATED_TYPE_IN_2_32_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_32
#define GIO_AVAILABLE_IN_2_32 GIO_UNAVAILABLE (2, 32)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_32 GLIB_UNAVAILABLE_STATIC_INLINE (2, 32)
#define GIO_AVAILABLE_MACRO_IN_2_32 GLIB_UNAVAILABLE_MACRO (2, 32)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_32 GLIB_UNAVAILABLE_ENUMERATOR (2, 32)
#define GIO_AVAILABLE_TYPE_IN_2_32 GLIB_UNAVAILABLE_TYPE (2, 32)
#else
#define GIO_AVAILABLE_IN_2_32 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_32
#define GIO_AVAILABLE_MACRO_IN_2_32
#define GIO_AVAILABLE_ENUMERATOR_IN_2_32
#define GIO_AVAILABLE_TYPE_IN_2_32
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_34
#define GIO_DEPRECATED_IN_2_34 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_34_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_34 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_34_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_34 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_34_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_34 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_34_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_34 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_34_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_34
#define GIO_DEPRECATED_MACRO_IN_2_34_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_34
#define GIO_DEPRECATED_ENUMERATOR_IN_2_34_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_34
#define GIO_DEPRECATED_TYPE_IN_2_34_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_34
#define GIO_AVAILABLE_IN_2_34 GIO_UNAVAILABLE (2, 34)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_34 GLIB_UNAVAILABLE_STATIC_INLINE (2, 34)
#define GIO_AVAILABLE_MACRO_IN_2_34 GLIB_UNAVAILABLE_MACRO (2, 34)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_34 GLIB_UNAVAILABLE_ENUMERATOR (2, 34)
#define GIO_AVAILABLE_TYPE_IN_2_34 GLIB_UNAVAILABLE_TYPE (2, 34)
#else
#define GIO_AVAILABLE_IN_2_34 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_34
#define GIO_AVAILABLE_MACRO_IN_2_34
#define GIO_AVAILABLE_ENUMERATOR_IN_2_34
#define GIO_AVAILABLE_TYPE_IN_2_34
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_36
#define GIO_DEPRECATED_IN_2_36 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_36_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_36 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_36_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_36 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_36_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_36 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_36_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_36 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_36_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_36
#define GIO_DEPRECATED_MACRO_IN_2_36_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_36
#define GIO_DEPRECATED_ENUMERATOR_IN_2_36_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_36
#define GIO_DEPRECATED_TYPE_IN_2_36_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_36
#define GIO_AVAILABLE_IN_2_36 GIO_UNAVAILABLE (2, 36)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_36 GLIB_UNAVAILABLE_STATIC_INLINE (2, 36)
#define GIO_AVAILABLE_MACRO_IN_2_36 GLIB_UNAVAILABLE_MACRO (2, 36)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_36 GLIB_UNAVAILABLE_ENUMERATOR (2, 36)
#define GIO_AVAILABLE_TYPE_IN_2_36 GLIB_UNAVAILABLE_TYPE (2, 36)
#else
#define GIO_AVAILABLE_IN_2_36 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_36
#define GIO_AVAILABLE_MACRO_IN_2_36
#define GIO_AVAILABLE_ENUMERATOR_IN_2_36
#define GIO_AVAILABLE_TYPE_IN_2_36
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_38
#define GIO_DEPRECATED_IN_2_38 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_38_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_38 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_38_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_38 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_38_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_38 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_38_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_38 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_38_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_38
#define GIO_DEPRECATED_MACRO_IN_2_38_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_38
#define GIO_DEPRECATED_ENUMERATOR_IN_2_38_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_38
#define GIO_DEPRECATED_TYPE_IN_2_38_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_38
#define GIO_AVAILABLE_IN_2_38 GIO_UNAVAILABLE (2, 38)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_38 GLIB_UNAVAILABLE_STATIC_INLINE (2, 38)
#define GIO_AVAILABLE_MACRO_IN_2_38 GLIB_UNAVAILABLE_MACRO (2, 38)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_38 GLIB_UNAVAILABLE_ENUMERATOR (2, 38)
#define GIO_AVAILABLE_TYPE_IN_2_38 GLIB_UNAVAILABLE_TYPE (2, 38)
#else
#define GIO_AVAILABLE_IN_2_38 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_38
#define GIO_AVAILABLE_MACRO_IN_2_38
#define GIO_AVAILABLE_ENUMERATOR_IN_2_38
#define GIO_AVAILABLE_TYPE_IN_2_38
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_40
#define GIO_DEPRECATED_IN_2_40 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_40_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_40 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_40_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_40 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_40_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_40 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_40_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_40 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_40_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_40
#define GIO_DEPRECATED_MACRO_IN_2_40_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_40
#define GIO_DEPRECATED_ENUMERATOR_IN_2_40_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_40
#define GIO_DEPRECATED_TYPE_IN_2_40_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_40
#define GIO_AVAILABLE_IN_2_40 GIO_UNAVAILABLE (2, 40)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_40 GLIB_UNAVAILABLE_STATIC_INLINE (2, 40)
#define GIO_AVAILABLE_MACRO_IN_2_40 GLIB_UNAVAILABLE_MACRO (2, 40)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_40 GLIB_UNAVAILABLE_ENUMERATOR (2, 40)
#define GIO_AVAILABLE_TYPE_IN_2_40 GLIB_UNAVAILABLE_TYPE (2, 40)
#else
#define GIO_AVAILABLE_IN_2_40 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_40
#define GIO_AVAILABLE_MACRO_IN_2_40
#define GIO_AVAILABLE_ENUMERATOR_IN_2_40
#define GIO_AVAILABLE_TYPE_IN_2_40
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_42
#define GIO_DEPRECATED_IN_2_42 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_42_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_42 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_42_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_42 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_42_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_42 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_42_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_42 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_42_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_42
#define GIO_DEPRECATED_MACRO_IN_2_42_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_42
#define GIO_DEPRECATED_ENUMERATOR_IN_2_42_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_42
#define GIO_DEPRECATED_TYPE_IN_2_42_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_42
#define GIO_AVAILABLE_IN_2_42 GIO_UNAVAILABLE (2, 42)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_42 GLIB_UNAVAILABLE_STATIC_INLINE (2, 42)
#define GIO_AVAILABLE_MACRO_IN_2_42 GLIB_UNAVAILABLE_MACRO (2, 42)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_42 GLIB_UNAVAILABLE_ENUMERATOR (2, 42)
#define GIO_AVAILABLE_TYPE_IN_2_42 GLIB_UNAVAILABLE_TYPE (2, 42)
#else
#define GIO_AVAILABLE_IN_2_42 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_42
#define GIO_AVAILABLE_MACRO_IN_2_42
#define GIO_AVAILABLE_ENUMERATOR_IN_2_42
#define GIO_AVAILABLE_TYPE_IN_2_42
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_44
#define GIO_DEPRECATED_IN_2_44 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_44_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_44 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_44_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_44 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_44_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_44 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_44_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_44 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_44_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_44
#define GIO_DEPRECATED_MACRO_IN_2_44_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_44
#define GIO_DEPRECATED_ENUMERATOR_IN_2_44_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_44
#define GIO_DEPRECATED_TYPE_IN_2_44_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_44
#define GIO_AVAILABLE_IN_2_44 GIO_UNAVAILABLE (2, 44)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_44 GLIB_UNAVAILABLE_STATIC_INLINE (2, 44)
#define GIO_AVAILABLE_MACRO_IN_2_44 GLIB_UNAVAILABLE_MACRO (2, 44)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_44 GLIB_UNAVAILABLE_ENUMERATOR (2, 44)
#define GIO_AVAILABLE_TYPE_IN_2_44 GLIB_UNAVAILABLE_TYPE (2, 44)
#else
#define GIO_AVAILABLE_IN_2_44 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_44
#define GIO_AVAILABLE_MACRO_IN_2_44
#define GIO_AVAILABLE_ENUMERATOR_IN_2_44
#define GIO_AVAILABLE_TYPE_IN_2_44
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_46
#define GIO_DEPRECATED_IN_2_46 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_46_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_46 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_46_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_46 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_46_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_46 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_46_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_46 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_46_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_46
#define GIO_DEPRECATED_MACRO_IN_2_46_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_46
#define GIO_DEPRECATED_ENUMERATOR_IN_2_46_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_46
#define GIO_DEPRECATED_TYPE_IN_2_46_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_46
#define GIO_AVAILABLE_IN_2_46 GIO_UNAVAILABLE (2, 46)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_46 GLIB_UNAVAILABLE_STATIC_INLINE (2, 46)
#define GIO_AVAILABLE_MACRO_IN_2_46 GLIB_UNAVAILABLE_MACRO (2, 46)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_46 GLIB_UNAVAILABLE_ENUMERATOR (2, 46)
#define GIO_AVAILABLE_TYPE_IN_2_46 GLIB_UNAVAILABLE_TYPE (2, 46)
#else
#define GIO_AVAILABLE_IN_2_46 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_46
#define GIO_AVAILABLE_MACRO_IN_2_46
#define GIO_AVAILABLE_ENUMERATOR_IN_2_46
#define GIO_AVAILABLE_TYPE_IN_2_46
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_48
#define GIO_DEPRECATED_IN_2_48 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_48_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_48 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_48_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_48 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_48_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_48 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_48_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_48 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_48_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_48
#define GIO_DEPRECATED_MACRO_IN_2_48_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_48
#define GIO_DEPRECATED_ENUMERATOR_IN_2_48_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_48
#define GIO_DEPRECATED_TYPE_IN_2_48_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_48
#define GIO_AVAILABLE_IN_2_48 GIO_UNAVAILABLE (2, 48)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_48 GLIB_UNAVAILABLE_STATIC_INLINE (2, 48)
#define GIO_AVAILABLE_MACRO_IN_2_48 GLIB_UNAVAILABLE_MACRO (2, 48)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_48 GLIB_UNAVAILABLE_ENUMERATOR (2, 48)
#define GIO_AVAILABLE_TYPE_IN_2_48 GLIB_UNAVAILABLE_TYPE (2, 48)
#else
#define GIO_AVAILABLE_IN_2_48 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_48
#define GIO_AVAILABLE_MACRO_IN_2_48
#define GIO_AVAILABLE_ENUMERATOR_IN_2_48
#define GIO_AVAILABLE_TYPE_IN_2_48
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_50
#define GIO_DEPRECATED_IN_2_50 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_50_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_50 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_50_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_50 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_50_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_50 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_50_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_50 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_50_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_50
#define GIO_DEPRECATED_MACRO_IN_2_50_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_50
#define GIO_DEPRECATED_ENUMERATOR_IN_2_50_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_50
#define GIO_DEPRECATED_TYPE_IN_2_50_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_50
#define GIO_AVAILABLE_IN_2_50 GIO_UNAVAILABLE (2, 50)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_50 GLIB_UNAVAILABLE_STATIC_INLINE (2, 50)
#define GIO_AVAILABLE_MACRO_IN_2_50 GLIB_UNAVAILABLE_MACRO (2, 50)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_50 GLIB_UNAVAILABLE_ENUMERATOR (2, 50)
#define GIO_AVAILABLE_TYPE_IN_2_50 GLIB_UNAVAILABLE_TYPE (2, 50)
#else
#define GIO_AVAILABLE_IN_2_50 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_50
#define GIO_AVAILABLE_MACRO_IN_2_50
#define GIO_AVAILABLE_ENUMERATOR_IN_2_50
#define GIO_AVAILABLE_TYPE_IN_2_50
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_52
#define GIO_DEPRECATED_IN_2_52 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_52_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_52 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_52_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_52 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_52_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_52 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_52_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_52 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_52_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_52
#define GIO_DEPRECATED_MACRO_IN_2_52_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_52
#define GIO_DEPRECATED_ENUMERATOR_IN_2_52_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_52
#define GIO_DEPRECATED_TYPE_IN_2_52_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_52
#define GIO_AVAILABLE_IN_2_52 GIO_UNAVAILABLE (2, 52)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_52 GLIB_UNAVAILABLE_STATIC_INLINE (2, 52)
#define GIO_AVAILABLE_MACRO_IN_2_52 GLIB_UNAVAILABLE_MACRO (2, 52)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_52 GLIB_UNAVAILABLE_ENUMERATOR (2, 52)
#define GIO_AVAILABLE_TYPE_IN_2_52 GLIB_UNAVAILABLE_TYPE (2, 52)
#else
#define GIO_AVAILABLE_IN_2_52 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_52
#define GIO_AVAILABLE_MACRO_IN_2_52
#define GIO_AVAILABLE_ENUMERATOR_IN_2_52
#define GIO_AVAILABLE_TYPE_IN_2_52
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_54
#define GIO_DEPRECATED_IN_2_54 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_54_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_54 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_54_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_54 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_54_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_54 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_54_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_54 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_54_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_54
#define GIO_DEPRECATED_MACRO_IN_2_54_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_54
#define GIO_DEPRECATED_ENUMERATOR_IN_2_54_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_54
#define GIO_DEPRECATED_TYPE_IN_2_54_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_54
#define GIO_AVAILABLE_IN_2_54 GIO_UNAVAILABLE (2, 54)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_54 GLIB_UNAVAILABLE_STATIC_INLINE (2, 54)
#define GIO_AVAILABLE_MACRO_IN_2_54 GLIB_UNAVAILABLE_MACRO (2, 54)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_54 GLIB_UNAVAILABLE_ENUMERATOR (2, 54)
#define GIO_AVAILABLE_TYPE_IN_2_54 GLIB_UNAVAILABLE_TYPE (2, 54)
#else
#define GIO_AVAILABLE_IN_2_54 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_54
#define GIO_AVAILABLE_MACRO_IN_2_54
#define GIO_AVAILABLE_ENUMERATOR_IN_2_54
#define GIO_AVAILABLE_TYPE_IN_2_54
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_56
#define GIO_DEPRECATED_IN_2_56 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_56_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_56 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_56_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_56 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_56_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_56 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_56_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_56 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_56_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_56
#define GIO_DEPRECATED_MACRO_IN_2_56_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_56
#define GIO_DEPRECATED_ENUMERATOR_IN_2_56_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_56
#define GIO_DEPRECATED_TYPE_IN_2_56_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_56
#define GIO_AVAILABLE_IN_2_56 GIO_UNAVAILABLE (2, 56)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_56 GLIB_UNAVAILABLE_STATIC_INLINE (2, 56)
#define GIO_AVAILABLE_MACRO_IN_2_56 GLIB_UNAVAILABLE_MACRO (2, 56)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_56 GLIB_UNAVAILABLE_ENUMERATOR (2, 56)
#define GIO_AVAILABLE_TYPE_IN_2_56 GLIB_UNAVAILABLE_TYPE (2, 56)
#else
#define GIO_AVAILABLE_IN_2_56 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_56
#define GIO_AVAILABLE_MACRO_IN_2_56
#define GIO_AVAILABLE_ENUMERATOR_IN_2_56
#define GIO_AVAILABLE_TYPE_IN_2_56
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_58
#define GIO_DEPRECATED_IN_2_58 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_58_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_58 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_58_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_58 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_58_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_58 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_58_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_58 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_58_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_58
#define GIO_DEPRECATED_MACRO_IN_2_58_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_58
#define GIO_DEPRECATED_ENUMERATOR_IN_2_58_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_58
#define GIO_DEPRECATED_TYPE_IN_2_58_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_58
#define GIO_AVAILABLE_IN_2_58 GIO_UNAVAILABLE (2, 58)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_58 GLIB_UNAVAILABLE_STATIC_INLINE (2, 58)
#define GIO_AVAILABLE_MACRO_IN_2_58 GLIB_UNAVAILABLE_MACRO (2, 58)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_58 GLIB_UNAVAILABLE_ENUMERATOR (2, 58)
#define GIO_AVAILABLE_TYPE_IN_2_58 GLIB_UNAVAILABLE_TYPE (2, 58)
#else
#define GIO_AVAILABLE_IN_2_58 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_58
#define GIO_AVAILABLE_MACRO_IN_2_58
#define GIO_AVAILABLE_ENUMERATOR_IN_2_58
#define GIO_AVAILABLE_TYPE_IN_2_58
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_60
#define GIO_DEPRECATED_IN_2_60 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_60_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_60 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_60_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_60 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_60_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_60 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_60_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_60 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_60_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_60
#define GIO_DEPRECATED_MACRO_IN_2_60_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_60
#define GIO_DEPRECATED_ENUMERATOR_IN_2_60_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_60
#define GIO_DEPRECATED_TYPE_IN_2_60_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_60
#define GIO_AVAILABLE_IN_2_60 GIO_UNAVAILABLE (2, 60)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_60 GLIB_UNAVAILABLE_STATIC_INLINE (2, 60)
#define GIO_AVAILABLE_MACRO_IN_2_60 GLIB_UNAVAILABLE_MACRO (2, 60)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_60 GLIB_UNAVAILABLE_ENUMERATOR (2, 60)
#define GIO_AVAILABLE_TYPE_IN_2_60 GLIB_UNAVAILABLE_TYPE (2, 60)
#else
#define GIO_AVAILABLE_IN_2_60 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_60
#define GIO_AVAILABLE_MACRO_IN_2_60
#define GIO_AVAILABLE_ENUMERATOR_IN_2_60
#define GIO_AVAILABLE_TYPE_IN_2_60
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_62
#define GIO_DEPRECATED_IN_2_62 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_62_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_62 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_62_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_62 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_62_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_62 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_62_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_62 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_62_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_62
#define GIO_DEPRECATED_MACRO_IN_2_62_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_62
#define GIO_DEPRECATED_ENUMERATOR_IN_2_62_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_62
#define GIO_DEPRECATED_TYPE_IN_2_62_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_62
#define GIO_AVAILABLE_IN_2_62 GIO_UNAVAILABLE (2, 62)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_62 GLIB_UNAVAILABLE_STATIC_INLINE (2, 62)
#define GIO_AVAILABLE_MACRO_IN_2_62 GLIB_UNAVAILABLE_MACRO (2, 62)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_62 GLIB_UNAVAILABLE_ENUMERATOR (2, 62)
#define GIO_AVAILABLE_TYPE_IN_2_62 GLIB_UNAVAILABLE_TYPE (2, 62)
#else
#define GIO_AVAILABLE_IN_2_62 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_62
#define GIO_AVAILABLE_MACRO_IN_2_62
#define GIO_AVAILABLE_ENUMERATOR_IN_2_62
#define GIO_AVAILABLE_TYPE_IN_2_62
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_64
#define GIO_DEPRECATED_IN_2_64 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_64_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_64 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_64_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_64 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_64_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_64 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_64_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_64 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_64_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_64
#define GIO_DEPRECATED_MACRO_IN_2_64_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_64
#define GIO_DEPRECATED_ENUMERATOR_IN_2_64_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_64
#define GIO_DEPRECATED_TYPE_IN_2_64_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_64
#define GIO_AVAILABLE_IN_2_64 GIO_UNAVAILABLE (2, 64)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_64 GLIB_UNAVAILABLE_STATIC_INLINE (2, 64)
#define GIO_AVAILABLE_MACRO_IN_2_64 GLIB_UNAVAILABLE_MACRO (2, 64)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_64 GLIB_UNAVAILABLE_ENUMERATOR (2, 64)
#define GIO_AVAILABLE_TYPE_IN_2_64 GLIB_UNAVAILABLE_TYPE (2, 64)
#else
#define GIO_AVAILABLE_IN_2_64 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_64
#define GIO_AVAILABLE_MACRO_IN_2_64
#define GIO_AVAILABLE_ENUMERATOR_IN_2_64
#define GIO_AVAILABLE_TYPE_IN_2_64
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_66
#define GIO_DEPRECATED_IN_2_66 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_66_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_66 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_66_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_66 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_66_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_66 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_66_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_66 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_66_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_66
#define GIO_DEPRECATED_MACRO_IN_2_66_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_66
#define GIO_DEPRECATED_ENUMERATOR_IN_2_66_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_66
#define GIO_DEPRECATED_TYPE_IN_2_66_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_66
#define GIO_AVAILABLE_IN_2_66 GIO_UNAVAILABLE (2, 66)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_66 GLIB_UNAVAILABLE_STATIC_INLINE (2, 66)
#define GIO_AVAILABLE_MACRO_IN_2_66 GLIB_UNAVAILABLE_MACRO (2, 66)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_66 GLIB_UNAVAILABLE_ENUMERATOR (2, 66)
#define GIO_AVAILABLE_TYPE_IN_2_66 GLIB_UNAVAILABLE_TYPE (2, 66)
#else
#define GIO_AVAILABLE_IN_2_66 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_66
#define GIO_AVAILABLE_MACRO_IN_2_66
#define GIO_AVAILABLE_ENUMERATOR_IN_2_66
#define GIO_AVAILABLE_TYPE_IN_2_66
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_68
#define GIO_DEPRECATED_IN_2_68 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_68_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_68 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_68_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_68 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_68_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_68 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_68_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_68 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_68_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_68
#define GIO_DEPRECATED_MACRO_IN_2_68_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_68
#define GIO_DEPRECATED_ENUMERATOR_IN_2_68_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_68
#define GIO_DEPRECATED_TYPE_IN_2_68_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_68
#define GIO_AVAILABLE_IN_2_68 GIO_UNAVAILABLE (2, 68)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_68 GLIB_UNAVAILABLE_STATIC_INLINE (2, 68)
#define GIO_AVAILABLE_MACRO_IN_2_68 GLIB_UNAVAILABLE_MACRO (2, 68)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_68 GLIB_UNAVAILABLE_ENUMERATOR (2, 68)
#define GIO_AVAILABLE_TYPE_IN_2_68 GLIB_UNAVAILABLE_TYPE (2, 68)
#else
#define GIO_AVAILABLE_IN_2_68 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_68
#define GIO_AVAILABLE_MACRO_IN_2_68
#define GIO_AVAILABLE_ENUMERATOR_IN_2_68
#define GIO_AVAILABLE_TYPE_IN_2_68
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_70
#define GIO_DEPRECATED_IN_2_70 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_70_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_70 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_70_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_70 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_70_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_70 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_70_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_70 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_70_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_70
#define GIO_DEPRECATED_MACRO_IN_2_70_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_70
#define GIO_DEPRECATED_ENUMERATOR_IN_2_70_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_70
#define GIO_DEPRECATED_TYPE_IN_2_70_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_70
#define GIO_AVAILABLE_IN_2_70 GIO_UNAVAILABLE (2, 70)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_70 GLIB_UNAVAILABLE_STATIC_INLINE (2, 70)
#define GIO_AVAILABLE_MACRO_IN_2_70 GLIB_UNAVAILABLE_MACRO (2, 70)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_70 GLIB_UNAVAILABLE_ENUMERATOR (2, 70)
#define GIO_AVAILABLE_TYPE_IN_2_70 GLIB_UNAVAILABLE_TYPE (2, 70)
#else
#define GIO_AVAILABLE_IN_2_70 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_70
#define GIO_AVAILABLE_MACRO_IN_2_70
#define GIO_AVAILABLE_ENUMERATOR_IN_2_70
#define GIO_AVAILABLE_TYPE_IN_2_70
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_72
#define GIO_DEPRECATED_IN_2_72 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_72_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_72 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_72_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_72 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_72_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_72 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_72_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_72 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_72_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_72
#define GIO_DEPRECATED_MACRO_IN_2_72_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_72
#define GIO_DEPRECATED_ENUMERATOR_IN_2_72_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_72
#define GIO_DEPRECATED_TYPE_IN_2_72_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_72
#define GIO_AVAILABLE_IN_2_72 GIO_UNAVAILABLE (2, 72)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_72 GLIB_UNAVAILABLE_STATIC_INLINE (2, 72)
#define GIO_AVAILABLE_MACRO_IN_2_72 GLIB_UNAVAILABLE_MACRO (2, 72)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_72 GLIB_UNAVAILABLE_ENUMERATOR (2, 72)
#define GIO_AVAILABLE_TYPE_IN_2_72 GLIB_UNAVAILABLE_TYPE (2, 72)
#else
#define GIO_AVAILABLE_IN_2_72 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_72
#define GIO_AVAILABLE_MACRO_IN_2_72
#define GIO_AVAILABLE_ENUMERATOR_IN_2_72
#define GIO_AVAILABLE_TYPE_IN_2_72
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_74
#define GIO_DEPRECATED_IN_2_74 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_74_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_74 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_74_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_74 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_74_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_74 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_74_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_74 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_74_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_74
#define GIO_DEPRECATED_MACRO_IN_2_74_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_74
#define GIO_DEPRECATED_ENUMERATOR_IN_2_74_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_74
#define GIO_DEPRECATED_TYPE_IN_2_74_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_74
#define GIO_AVAILABLE_IN_2_74 GIO_UNAVAILABLE (2, 74)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_74 GLIB_UNAVAILABLE_STATIC_INLINE (2, 74)
#define GIO_AVAILABLE_MACRO_IN_2_74 GLIB_UNAVAILABLE_MACRO (2, 74)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_74 GLIB_UNAVAILABLE_ENUMERATOR (2, 74)
#define GIO_AVAILABLE_TYPE_IN_2_74 GLIB_UNAVAILABLE_TYPE (2, 74)
#else
#define GIO_AVAILABLE_IN_2_74 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_74
#define GIO_AVAILABLE_MACRO_IN_2_74
#define GIO_AVAILABLE_ENUMERATOR_IN_2_74
#define GIO_AVAILABLE_TYPE_IN_2_74
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_76
#define GIO_DEPRECATED_IN_2_76 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_76_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_76 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_76_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_76 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_76_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_76 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_76_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_76 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_76_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_76
#define GIO_DEPRECATED_MACRO_IN_2_76_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_76
#define GIO_DEPRECATED_ENUMERATOR_IN_2_76_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_76
#define GIO_DEPRECATED_TYPE_IN_2_76_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_76
#define GIO_AVAILABLE_IN_2_76 GIO_UNAVAILABLE (2, 76)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_76 GLIB_UNAVAILABLE_STATIC_INLINE (2, 76)
#define GIO_AVAILABLE_MACRO_IN_2_76 GLIB_UNAVAILABLE_MACRO (2, 76)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_76 GLIB_UNAVAILABLE_ENUMERATOR (2, 76)
#define GIO_AVAILABLE_TYPE_IN_2_76 GLIB_UNAVAILABLE_TYPE (2, 76)
#else
#define GIO_AVAILABLE_IN_2_76 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_76
#define GIO_AVAILABLE_MACRO_IN_2_76
#define GIO_AVAILABLE_ENUMERATOR_IN_2_76
#define GIO_AVAILABLE_TYPE_IN_2_76
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_78
#define GIO_DEPRECATED_IN_2_78 GIO_DEPRECATED
#define GIO_DEPRECATED_IN_2_78_FOR(f) GIO_DEPRECATED_FOR (f)
#define GIO_DEPRECATED_MACRO_IN_2_78 GLIB_DEPRECATED_MACRO
#define GIO_DEPRECATED_MACRO_IN_2_78_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_78 GLIB_DEPRECATED_ENUMERATOR
#define GIO_DEPRECATED_ENUMERATOR_IN_2_78_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GIO_DEPRECATED_TYPE_IN_2_78 GLIB_DEPRECATED_TYPE
#define GIO_DEPRECATED_TYPE_IN_2_78_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GIO_DEPRECATED_IN_2_78 _GIO_EXTERN
#define GIO_DEPRECATED_IN_2_78_FOR(f) _GIO_EXTERN
#define GIO_DEPRECATED_MACRO_IN_2_78
#define GIO_DEPRECATED_MACRO_IN_2_78_FOR(f)
#define GIO_DEPRECATED_ENUMERATOR_IN_2_78
#define GIO_DEPRECATED_ENUMERATOR_IN_2_78_FOR(f)
#define GIO_DEPRECATED_TYPE_IN_2_78
#define GIO_DEPRECATED_TYPE_IN_2_78_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_78
#define GIO_AVAILABLE_IN_2_78 GIO_UNAVAILABLE (2, 78)
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_78 GLIB_UNAVAILABLE_STATIC_INLINE (2, 78)
#define GIO_AVAILABLE_MACRO_IN_2_78 GLIB_UNAVAILABLE_MACRO (2, 78)
#define GIO_AVAILABLE_ENUMERATOR_IN_2_78 GLIB_UNAVAILABLE_ENUMERATOR (2, 78)
#define GIO_AVAILABLE_TYPE_IN_2_78 GLIB_UNAVAILABLE_TYPE (2, 78)
#else
#define GIO_AVAILABLE_IN_2_78 _GIO_EXTERN
#define GIO_AVAILABLE_STATIC_INLINE_IN_2_78
#define GIO_AVAILABLE_MACRO_IN_2_78
#define GIO_AVAILABLE_ENUMERATOR_IN_2_78
#define GIO_AVAILABLE_TYPE_IN_2_78
#endif
